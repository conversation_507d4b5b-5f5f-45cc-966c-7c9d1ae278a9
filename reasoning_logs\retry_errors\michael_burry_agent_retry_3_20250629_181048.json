{"error_type": "RateLimitError", "error_message": "Error code: 429 - {'error': {'message': 'Rate limit reached for model `meta-llama/llama-4-scout-17b-16e-instruct` in organization `org_01jy5wa099frrsnynxryzrag6f` service tier `on_demand` on tokens per day (TPD): Limit 500000, Used 499985, Requested 921. Please try again in 2m36.4188s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}", "model_name": "meta-llama/llama-4-scout-17b-16e-instruct", "model_provider": "Groq", "agent_name": "mi<PERSON><PERSON>_burry_agent", "attempt_number": 3, "max_retries": 3, "timestamp": "2025-06-29T18:10:48.072476"}