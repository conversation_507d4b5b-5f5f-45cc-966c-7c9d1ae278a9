{"error_type": "RateLimitError", "error_message": "Error code: 429 - {'error': {'message': 'Rate limit reached for model `meta-llama/llama-4-scout-17b-16e-instruct` in organization `org_01jxy7cjwefrjtey17aw1ywjr3` service tier `on_demand` on tokens per day (TPD): Limit 500000, Used 502520, Requested 6479. Please try again in 25m55.062s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}", "model_name": "meta-llama/llama-4-scout-17b-16e-instruct", "model_provider": "Groq", "agent_name": "portfolio_manager", "attempt_number": 3, "max_retries": 3, "timestamp": "2025-06-29T18:10:54.280155"}