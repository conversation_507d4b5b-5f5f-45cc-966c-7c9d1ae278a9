"""GROQ API密钥管理器，支持多个API密钥的轮换以避免速率限制"""

import os
import logging
from typing import List, Optional
import threading

logger = logging.getLogger(__name__)


class GroqApiManager:
    """GROQ API密钥管理器，支持自动轮换API密钥以避免速率限制"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局只有一个API管理器实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化API密钥管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.api_keys: List[str] = []
        self.current_key_index: int = 0
        self.failed_keys: set = set()  # 记录失败的API密钥
        self._load_api_keys()
    
    def _load_api_keys(self):
        """从环境变量动态加载所有GROQ API密钥"""
        # 动态检测所有以"GROQ_API_KEY"开头的环境变量
        groq_keys = {}

        # 遍历所有环境变量，找到以GROQ_API_KEY开头的
        for env_var, value in os.environ.items():
            if env_var.startswith('GROQ_API_KEY') and value and value.strip():
                groq_keys[env_var] = value.strip()

        # 按照密钥名称排序，确保一致的加载顺序
        # 优先级：GROQ_API_KEY1-4 > GROQ_API_KEY11+ > GROQ_API_KEY (默认)
        sorted_keys = []

        # 首先添加GROQ_API_KEY1-4（原有的密钥）
        for i in range(1, 5):
            key_name = f'GROQ_API_KEY{i}'
            if key_name in groq_keys:
                sorted_keys.append((key_name, groq_keys[key_name]))

        # 然后添加其他编号的密钥（如GROQ_API_KEY11等）
        other_numbered_keys = []
        for key_name in groq_keys:
            if key_name.startswith('GROQ_API_KEY') and key_name not in [f'GROQ_API_KEY{i}' for i in range(1, 5)] and key_name != 'GROQ_API_KEY':
                # 提取数字进行排序
                try:
                    number = int(key_name.replace('GROQ_API_KEY', ''))
                    other_numbered_keys.append((number, key_name, groq_keys[key_name]))
                except ValueError:
                    # 如果不是纯数字后缀，按字母顺序排序
                    other_numbered_keys.append((float('inf'), key_name, groq_keys[key_name]))

        # 按数字排序其他密钥
        other_numbered_keys.sort(key=lambda x: x[0])
        for _, key_name, value in other_numbered_keys:
            sorted_keys.append((key_name, value))

        # 最后添加默认的GROQ_API_KEY（如果存在且没有其他密钥）
        if 'GROQ_API_KEY' in groq_keys and not sorted_keys:
            sorted_keys.append(('GROQ_API_KEY', groq_keys['GROQ_API_KEY']))

        # 加载所有找到的密钥
        for key_name, api_key in sorted_keys:
            self.api_keys.append(api_key)
            logger.info(f"已加载 {key_name}")

        if not self.api_keys:
            logger.warning("未找到任何GROQ API密钥")
        else:
            logger.info(f"总共加载了 {len(self.api_keys)} 个GROQ API密钥")
            # 显示所有加载的密钥名称（用于调试）
            key_names = [key_name for key_name, _ in sorted_keys]
            logger.info(f"加载的密钥: {', '.join(key_names)}")
    
    def get_current_api_key(self) -> Optional[str]:
        """获取当前可用的API密钥"""
        if not self.api_keys:
            logger.error("没有可用的GROQ API密钥")
            return None
        
        # 如果当前密钥已失败，尝试下一个
        attempts = 0
        while attempts < len(self.api_keys):
            current_key = self.api_keys[self.current_key_index]
            
            # 如果当前密钥没有失败，返回它
            if current_key not in self.failed_keys:
                logger.debug(f"使用API密钥索引: {self.current_key_index + 1}")
                return current_key
            
            # 当前密钥已失败，尝试下一个
            self._switch_to_next_key()
            attempts += 1
        
        # 所有密钥都失败了，重置失败记录并返回第一个密钥
        logger.warning("所有API密钥都已失败，重置失败记录")
        self.failed_keys.clear()
        self.current_key_index = 0
        return self.api_keys[0] if self.api_keys else None
    
    def _switch_to_next_key(self):
        """切换到下一个API密钥"""
        if len(self.api_keys) > 1:
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            logger.info(f"切换到API密钥索引: {self.current_key_index + 1}")
    
    def mark_key_as_failed(self, api_key: str):
        """标记API密钥为失败状态"""
        if api_key in self.api_keys:
            self.failed_keys.add(api_key)
            key_index = self.api_keys.index(api_key) + 1
            logger.warning(f"API密钥 {key_index} 已标记为失败")
    
    def handle_rate_limit_error(self, error_message: str) -> bool:
        """
        处理速率限制错误，切换到下一个API密钥
        
        Args:
            error_message: 错误消息
            
        Returns:
            bool: 如果成功切换到新的API密钥返回True，否则返回False
        """
        current_key = self.get_current_api_key()
        if current_key:
            # 标记当前密钥为失败
            self.mark_key_as_failed(current_key)
            
            # 切换到下一个密钥
            self._switch_to_next_key()
            
            # 检查是否有可用的密钥
            next_key = self.get_current_api_key()
            if next_key and next_key != current_key:
                logger.info(f"由于速率限制错误，已切换到新的API密钥")
                return True
        
        logger.error("无法切换到新的API密钥，所有密钥可能都已失败")
        return False
    
    def reset_failed_keys(self):
        """重置所有失败的密钥状态"""
        self.failed_keys.clear()
        logger.info("已重置所有失败的API密钥状态")

    def reload_api_keys(self):
        """重新加载API密钥（用于运行时检测新添加的密钥）"""
        logger.info("重新加载GROQ API密钥...")
        old_count = len(self.api_keys)

        # 清空现有密钥并重新加载
        self.api_keys.clear()
        self.failed_keys.clear()
        self.current_key_index = 0
        self._load_api_keys()

        new_count = len(self.api_keys)
        if new_count != old_count:
            logger.info(f"API密钥数量从 {old_count} 更新为 {new_count}")
        else:
            logger.info("API密钥数量未发生变化")
    
    def get_available_keys_count(self) -> int:
        """获取可用API密钥的数量"""
        return len(self.api_keys) - len(self.failed_keys)
    
    def get_status(self) -> dict:
        """获取API管理器的状态信息"""
        # 获取当前密钥的简短显示（前8位+...）
        current_key = self.get_current_api_key()
        current_key_display = f"{current_key[:8]}..." if current_key else "None"

        return {
            "total_keys": len(self.api_keys),
            "current_key_index": self.current_key_index + 1 if self.api_keys else 0,
            "current_key_preview": current_key_display,
            "failed_keys_count": len(self.failed_keys),
            "available_keys_count": self.get_available_keys_count()
        }

    def get_detailed_status(self) -> dict:
        """获取详细的API管理器状态信息"""
        # 重新检测环境变量中的密钥名称
        groq_env_keys = [key for key in os.environ.keys() if key.startswith('GROQ_API_KEY')]
        groq_env_keys.sort()

        return {
            "total_keys": len(self.api_keys),
            "current_key_index": self.current_key_index + 1 if self.api_keys else 0,
            "failed_keys_count": len(self.failed_keys),
            "available_keys_count": self.get_available_keys_count(),
            "detected_env_keys": groq_env_keys,
            "loaded_keys_count": len(self.api_keys)
        }


# 全局API管理器实例
groq_api_manager = GroqApiManager()


def get_groq_api_key() -> Optional[str]:
    """获取当前可用的GROQ API密钥"""
    return groq_api_manager.get_current_api_key()


def handle_groq_rate_limit(error_message: str) -> bool:
    """处理GROQ速率限制错误"""
    return groq_api_manager.handle_rate_limit_error(error_message)


def is_groq_rate_limit_error(error_message: str) -> bool:
    """检查是否为GROQ速率限制错误"""
    error_lower = error_message.lower()
    rate_limit_indicators = [
        "rate limit reached",
        "rate limit exceeded",
        "429",
        "requests per day",
        "rpd",
        "too many requests"
    ]

    return any(indicator in error_lower for indicator in rate_limit_indicators)


def is_groq_connection_error(error_message: str) -> bool:
    """检查是否为GROQ连接错误"""
    error_lower = error_message.lower()
    connection_indicators = [
        "ssl",
        "unexpected_eof_while_reading",
        "eof occurred in violation of protocol",
        "connecterror",
        "connection",
        "timeout",
        "network",
        "socket",
        "certificate",
        "handshake"
    ]

    return any(indicator in error_lower for indicator in connection_indicators)


def handle_groq_connection_error(error_message: str) -> bool:
    """处理GROQ连接错误，尝试切换API密钥"""
    logger.warning(f"检测到GROQ连接错误: {error_message[:100]}...")

    # 对于连接错误，也尝试切换API密钥，因为不同的密钥可能连接到不同的服务器
    return groq_api_manager.handle_rate_limit_error(f"Connection error: {error_message}")


def reload_groq_api_keys():
    """重新加载GROQ API密钥"""
    return groq_api_manager.reload_api_keys()


def get_groq_detailed_status() -> dict:
    """获取GROQ API管理器的详细状态"""
    return groq_api_manager.get_detailed_status()
