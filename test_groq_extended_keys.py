#!/usr/bin/env python3
"""
测试扩展的Groq API密钥检测和轮换功能
"""

import os
import sys
import time
from dotenv import load_dotenv

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.groq_api_manager import (
    GroqApiManager, 
    get_groq_api_key, 
    reload_groq_api_keys,
    get_groq_detailed_status,
    handle_groq_rate_limit
)

def print_separator(title):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def test_api_key_detection():
    """测试API密钥检测功能"""
    print_separator("API密钥检测测试")

    # 强制重新加载环境变量
    load_dotenv(override=True)

    # 手动检查.env文件中的所有GROQ密钥
    print("🔍 直接检查.env文件中的GROQ密钥...")
    env_file_keys = []
    env_file_values = {}
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line.startswith('GROQ_API_KEY') and '=' in line:
                    key_name, key_value = line.split('=', 1)
                    env_file_keys.append(key_name)
                    env_file_values[key_name] = key_value
                    print(f"   📄 第{line_num}行: {key_name}")
    except Exception as e:
        print(f"   ❌ 读取.env文件失败: {e}")

    print(f"📄 .env文件中找到 {len(env_file_keys)} 个GROQ密钥")

    # 手动设置环境变量（模拟.env文件中的所有密钥）
    print("\n🔧 手动设置环境变量...")
    for key_name, key_value in env_file_values.items():
        os.environ[key_name] = key_value
        print(f"   ✅ 设置 {key_name}")

    # 检查环境变量中实际加载的密钥
    print("\n🔍 检查环境变量中加载的GROQ密钥...")
    loaded_keys = []
    for key in env_file_keys:
        value = os.getenv(key)
        if value:
            loaded_keys.append(key)
            print(f"   ✅ {key}: {value[:8]}...")
        else:
            print(f"   ❌ {key}: 未加载")

    print(f"🔄 环境变量中加载了 {len(loaded_keys)} 个GROQ密钥")

    # 重新加载API管理器以检测新的密钥
    print("\n🔄 重新加载API管理器...")
    reload_groq_api_keys()

    # 获取详细状态
    status = get_groq_detailed_status()
    
    print(f"📊 检测到的环境变量密钥: {len(status['detected_env_keys'])}")
    for key in status['detected_env_keys']:
        value = os.getenv(key)
        if value:
            print(f"   ✅ {key}: {value[:8]}...")
        else:
            print(f"   ❌ {key}: 空值")
    
    print(f"\n📈 加载到管理器的密钥数量: {status['loaded_keys_count']}")
    print(f"📈 当前密钥索引: {status['current_key_index']}")
    print(f"📈 可用密钥数量: {status['available_keys_count']}")
    print(f"📈 失败密钥数量: {status['failed_keys_count']}")
    
    return status['loaded_keys_count'] > 0

def test_api_key_functionality():
    """测试API密钥功能"""
    print_separator("API密钥功能测试")
    
    # 创建新的管理器实例进行测试
    manager = GroqApiManager()
    
    print(f"🔑 总密钥数量: {len(manager.api_keys)}")
    
    if not manager.api_keys:
        print("❌ 没有找到任何API密钥")
        return False
    
    # 测试获取当前密钥
    current_key = manager.get_current_api_key()
    if current_key:
        print(f"✅ 当前密钥: {current_key[:8]}...")
    else:
        print("❌ 无法获取当前密钥")
        return False
    
    # 测试密钥轮换
    if len(manager.api_keys) > 1:
        print(f"\n🔄 测试密钥轮换功能...")
        original_key = current_key
        original_index = manager.current_key_index
        
        # 模拟速率限制错误
        success = manager.handle_rate_limit_error("Rate limit reached for testing")
        if success:
            new_key = manager.get_current_api_key()
            new_index = manager.current_key_index
            
            if new_key != original_key:
                print(f"✅ 密钥轮换成功")
                print(f"   原密钥索引: {original_index + 1} -> 新密钥索引: {new_index + 1}")
                print(f"   原密钥: {original_key[:8]}... -> 新密钥: {new_key[:8]}...")
            else:
                print("⚠️ 密钥轮换后仍是同一个密钥")
        else:
            print("❌ 密钥轮换失败")
    else:
        print("ℹ️ 只有一个密钥，无法测试轮换功能")
    
    return True

def test_runtime_key_reload():
    """测试运行时密钥重新加载"""
    print_separator("运行时密钥重新加载测试")
    
    # 获取当前状态
    status_before = get_groq_detailed_status()
    print(f"📊 重新加载前: {status_before['loaded_keys_count']} 个密钥")
    
    # 重新加载密钥
    print("🔄 执行密钥重新加载...")
    reload_groq_api_keys()
    
    # 获取重新加载后的状态
    status_after = get_groq_detailed_status()
    print(f"📊 重新加载后: {status_after['loaded_keys_count']} 个密钥")
    
    if status_after['loaded_keys_count'] >= status_before['loaded_keys_count']:
        print("✅ 密钥重新加载成功")
        return True
    else:
        print("❌ 密钥重新加载后数量减少")
        return False

def test_all_keys_availability():
    """测试所有密钥的可用性"""
    print_separator("所有密钥可用性测试")
    
    manager = GroqApiManager()
    
    if not manager.api_keys:
        print("❌ 没有找到任何API密钥")
        return False
    
    print(f"🧪 测试 {len(manager.api_keys)} 个API密钥的可用性...")
    
    available_count = 0
    for i, api_key in enumerate(manager.api_keys):
        try:
            # 这里只是检查密钥格式，不实际调用API
            if api_key and len(api_key) > 10 and api_key.startswith('gsk_'):
                print(f"   ✅ 密钥 {i+1}: {api_key[:8]}... (格式正确)")
                available_count += 1
            else:
                print(f"   ❌ 密钥 {i+1}: {api_key[:8]}... (格式错误)")
        except Exception as e:
            print(f"   ❌ 密钥 {i+1}: 检查时出错 - {str(e)}")
    
    print(f"\n📈 可用密钥: {available_count}/{len(manager.api_keys)}")
    success_rate = (available_count / len(manager.api_keys)) * 100
    print(f"📈 成功率: {success_rate:.1f}%")
    
    return available_count > 0

def main():
    """主测试函数"""
    print("🚀 Groq API扩展密钥检测测试")
    print("="*60)
    
    # 运行所有测试
    tests = [
        ("API密钥检测", test_api_key_detection),
        ("API密钥功能", test_api_key_functionality),
        ("运行时重新加载", test_runtime_key_reload),
        ("密钥可用性", test_all_keys_availability)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"\n❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
    
    # 显示测试总结
    print_separator("测试总结")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📊 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！扩展的API密钥功能工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
