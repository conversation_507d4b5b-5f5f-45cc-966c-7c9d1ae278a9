{"error_type": "RateLimitError", "error_message": "Error code: 429 - {'error': {'message': 'Rate limit reached for model `meta-llama/llama-4-scout-17b-16e-instruct` in organization `org_01jvp8m127e8rsjsprsy7npj72` service tier `on_demand` on tokens per day (TPD): Limit 500000, Used 504279, Requested 1122. Please try again in 15m33.414399999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}", "model_name": "meta-llama/llama-4-scout-17b-16e-instruct", "model_provider": "Groq", "agent_name": "bill_ackman_agent", "attempt_number": 2, "max_retries": 3, "timestamp": "2025-06-29T18:10:45.075680"}